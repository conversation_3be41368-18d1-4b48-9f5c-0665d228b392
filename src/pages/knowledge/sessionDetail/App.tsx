import {
    <PERSON><PERSON>,
    Card,
    ConfigProvider,
    Descriptions,
    Divider,
    Dropdown,
    Flex,
    Input,
    List,
    message,
    Row,
    Skeleton,
    Space,
    Tag,
} from 'antd';
import { useDebounceFn, useInfiniteScroll, useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import './style.scss';
import ListItem from '@src/pages/knowledge/sessionDetail/ListItem';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useState } from 'react';
import { SessionDetailResponse } from './types';
import { getCurrentMockData, mockConversations, setMockType } from './mock';

const App = () => {
    const sessionId = new URL(window.location.href).searchParams.get('sessionId');

    // 开发环境下使用mock数据的开关
    const useMockData = process.env.NODE_ENV === 'development';

    const { data, refresh: refreshBaseInfo } = useRequest<SessionDetailResponse | null, []>(async () => {
        if (!sessionId && !useMockData) {
            return null;
        }

        // 如果启用mock数据，直接返回mock数据
        if (useMockData) {
            const mockData = getCurrentMockData();
            setComment(mockData?.remark || '');
            return mockData;
        }

        const res = await apiCaller.get('/manage/session/detail', { sessionId });
        if (res.code === 0) {
            setComment(res.data?.remark);
            return res.data;
        }
        return null;
    });

    const {
        data: conversationsData,
        loadMore,
        noMore,
        loading,
    } = useInfiniteScroll<{ list: any[]; pageNum: number; total: number }>(
        async d => {
            const pageNum = d?.pageNum ?? 1;
            const list = d?.list || [];

            if (!sessionId && !useMockData) {
                return { list: [], pageNum, total: 0 };
            }

            // 如果启用mock数据，返回mock对话数据
            if (useMockData) {
                return {
                    list: mockConversations.conversations || [],
                    pageNum: pageNum + 1,
                    total: mockConversations.total,
                };
            }

            const res = await apiCaller.post('/manage/session/conversations', {
                sessionId: Number(sessionId),
                pageNum,
                pageSize: 20,
            });
            if (res.code === 0) {
                return {
                    list: res.data.conversations || [],
                    pageNum: pageNum + 1,
                    total: res.data.total,
                };
            }
            return { list, pageNum, total: d?.total || 0 };
        },
        {
            isNoMore: d => {
                if (!d?.total || !d?.list.length) {
                    return false;
                }
                return d?.total <= d?.list?.length;
            },
        },
    );

    const [editable, setEditable] = useState(false);
    const finalDisabled = !data?.remark ? false : !editable;
    const [comment, setComment] = useState('');
    const commitComment = useDebounceFn(
        async () => {
            if (finalDisabled) {
                setEditable(true);
                return Promise.resolve();
            }
            if (!comment) {
                message.warning('请先填写备注');
                return Promise.resolve();
            }
            const res = await apiCaller.post('/manage/session/comment', {
                sessionId: Number(sessionId),
                comment,
            });
            if (res.code === 0) {
                message.success(res.msg || '提交成功');
                refreshBaseInfo();
                setEditable(false);
                return Promise.resolve();
            }
            return Promise.resolve();
        },
        { wait: 200 },
    );

    if (!data) {
        return null;
    }
    const list = conversationsData?.list.reverse();

    // 处理查看TT工单功能
    const handleViewTTTicket = () => {
        if (data.ttLinkList && data.ttLinkList.length > 0) {
            // 如果只有一个链接，直接打开
            if (data.ttLinkList.length === 1) {
                window.open(data.ttLinkList[0].link, '_blank');
            }
            // 多个链接的情况通过下拉菜单处理，这里不需要额外逻辑
        }
    };

    // 生成下拉菜单项
    const generateMenuItems = () => {
        if (!data.ttLinkList || data.ttLinkList.length === 0) {
            return [];
        }

        return data.ttLinkList.map(item => ({
            key: item.id,
            label: <span onClick={() => window.open(item.link, '_blank')}>TT工单 {item.id}</span>,
        }));
    };

    return (
        <div style={{ display: 'flex', flexDirection: 'column', height: '100%', paddingBottom: 20 }}>
            {/* 开发环境下的mock数据控制面板 */}
            {process.env.NODE_ENV === 'development' && (
                <div style={{ marginBottom: 16, padding: 12, background: '#f0f0f0', borderRadius: 4 }}>
                    <Space>
                        <span>Mock数据测试:</span>
                        <Button
                            size="small"
                            type={useMockData ? 'primary' : 'default'}
                            onClick={() => {
                                localStorage.setItem('useMockData', useMockData ? 'false' : 'true');
                                window.location.reload();
                            }}
                        >
                            {useMockData ? '关闭Mock' : '启用Mock'}
                        </Button>
                        {useMockData && (
                            <>
                                <Button size="small" onClick={() => setMockType('single')}>
                                    单个TT工单
                                </Button>
                                <Button size="small" onClick={() => setMockType('multiple')}>
                                    多个TT工单
                                </Button>
                                <Button size="small" onClick={() => setMockType('noTT')}>
                                    无TT工单
                                </Button>
                            </>
                        )}
                    </Space>
                </div>
            )}
            <Space align={'center'}>
                <h2 style={{ marginBottom: 0 }}>会话 {data.id}</h2>
                {data.submittedTt && (
                    <Tag color={'red'} style={{ height: 22 }}>
                        已转TT工单
                    </Tag>
                )}
                {data.remark && (
                    <Tag color={'blue'} style={{ height: 22 }}>
                        已备注
                    </Tag>
                )}
            </Space>
            <Row style={{ color: '#999', marginTop: 4 }}>{data.createTime}</Row>
            <Flex justify={'space-between'} align="flex-end" style={{ width: '49%' }}>
                <Descriptions column={1} style={{ marginTop: 22 }}>
                    <Descriptions.Item label={'发问人'}>
                        <Space direction={'vertical'}>
                            <Space>
                                <span>{data.name}</span>
                                <span style={{ color: '#999' }}>{data.mis}</span>
                            </Space>
                        </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label={'组织架构'}>
                        <Space direction={'vertical'}>{data.orgInfos.map(v => v)}</Space>
                    </Descriptions.Item>
                    <Descriptions.Item label={'涉及域'}>
                        <Space direction={'vertical'}>{data.domainList.join('、') || '-'}</Space>
                    </Descriptions.Item>
                    <Descriptions.Item label={'标准问'} style={{ marginTop: 22 }}>
                        <Space>
                            {data.phraseNameList.map(v => (
                                <Tag color={'#7E818E14'} style={{ color: '#222' }} key={v}>
                                    {v}
                                </Tag>
                            ))}
                        </Space>
                    </Descriptions.Item>
                </Descriptions>
                {data.ttLinkList && data.ttLinkList.length > 0 && (
                    <>
                        {data.ttLinkList.length === 1 ? (
                            <Button type="primary" onClick={handleViewTTTicket}>
                                查看TT工单
                            </Button>
                        ) : (
                            <Dropdown
                                menu={{
                                    items: generateMenuItems(),
                                }}
                                placement="bottomLeft"
                            >
                                <Button type="primary">查看TT工单</Button>
                            </Dropdown>
                        )}
                    </>
                )}
            </Flex>

            <div style={{ display: 'flex', height: 566, marginTop: 22 }}>
                <Card
                    style={{
                        width: '50%',
                        marginRight: 10,
                        background: '#f4f4f4',
                        border: '1px solid #ccc',
                    }}
                >
                    <div
                        style={{
                            width: '100%',
                            display: 'flex',
                            overflow: 'hidden',
                            flexDirection: 'column',
                            height: 518,
                        }}
                    >
                        聊天记录
                        <Divider />
                        <div
                            style={{ overflow: 'scroll', flex: 1, display: 'flex', flexDirection: 'column-reverse' }}
                            id={'scrollableDiv'}
                        >
                            <InfiniteScroll
                                style={{ display: 'flex', flexDirection: 'column-reverse' }} // 使新数据加载在顶部
                                inverse={true}
                                dataLength={list?.length || 0}
                                next={() => {
                                    loadMore();
                                }}
                                hasMore={!noMore}
                                loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                                endMessage={<Divider plain>再也没有啦 🤐</Divider>}
                                scrollableTarget="scrollableDiv"
                            >
                                <List
                                    loading={loading}
                                    dataSource={list}
                                    renderItem={v => {
                                        return <ListItem data={v} />;
                                    }}
                                />
                            </InfiniteScroll>
                        </div>
                    </div>
                </Card>
                <Card style={{ width: '50%', background: '#f4f4f4', border: '1px solid #ccc' }}>
                    <Space direction={'vertical'} style={{ width: '100%' }} size={'middle'}>
                        备注内容
                        <ConfigProvider
                            theme={{
                                components: {
                                    Input: {
                                        colorBgContainerDisabled: '#f4f4f4',
                                        colorBgContainer: '#fff',
                                    },
                                },
                            }}
                        >
                            <Input.TextArea
                                disabled={finalDisabled}
                                defaultValue={data?.remark}
                                value={comment}
                                onChange={v => setComment(v.target.value)}
                                rows={19}
                                maxLength={100}
                                showCount={{
                                    formatter: ({ count, maxLength }) => (
                                        <div className={'transform-y'}>
                                            {count}/{maxLength}
                                        </div>
                                    ),
                                }}
                            />
                        </ConfigProvider>
                        <Row justify={'end'}>
                            <Space>
                                <Button type={'primary'} onClick={commitComment.run}>
                                    {finalDisabled ? '修改' : '确定'}
                                </Button>
                                {data.remark && !finalDisabled ? (
                                    <Button
                                        onClick={() => {
                                            setEditable(false);
                                            setComment(data.remark);
                                        }}
                                    >
                                        取消
                                    </Button>
                                ) : null}
                            </Space>
                        </Row>
                    </Space>
                </Card>
            </div>
        </div>
    );
};
export default App;
